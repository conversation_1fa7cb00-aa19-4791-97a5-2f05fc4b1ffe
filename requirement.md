\# 本地照片整理与电子相册生成需求说明文档  

\*\*版本：\*\* 1.2  

\*\*日期：\*\* 2025年9月13日  

\*\*运行环境：\*\* ThinkBook 14 (AMD R7-8845H, Radeon 780M集成显卡, 32GB RAM)  



---



\## 一、项目目标

1\. \*\*阶段一：照片结构化迁移\*\*  

&nbsp;  - 将原始照片按日期重新整理到全新独立目录

2\. \*\*阶段二：智能电子相册生成\*\*  

&nbsp;  - 实现人脸分类、AI视频生成等轻量化功能



---



\## 二、核心需求说明

\### 阶段一：照片结构化迁移

| \*\*功能\*\*         | \*\*详细要求\*\*                                                                 |

|------------------|-----------------------------------------------------------------------------|

| 输入源           | 本地任意文件夹（只读不修改）                                                |

| 输出目录         | 新建独立根目录（如 `D:\\SortedPhotos\\`）                                     |

| 目录结构         | `年\\年-月\\年-月-日\\`（例：`2025\\2025-09\\2025-09-13\\`）                     |

| 文件命名         | 保留原名或`日期\_时间\_序号.jpg`格式                                          |

| 去重机制         | 基于文件哈希值删除重复文件                                                  |



\### 阶段二：电子相册智能处理

| \*\*功能\*\*           | \*\*详细要求\*\*                                                                 |

|--------------------|-----------------------------------------------------------------------------|

| 人脸识别分类       | 使用MobileFaceNet模型，支持姓名标签存储                                     |

| AI精选视频生成     | 自动生成1080P短视频（15-30秒），集成在线音乐                               |

| 智能标签扩展       | 自动添加场景标签（海滩/婚礼等）                                             |

| 电子相册预览       | 生成网页版三维浏览视图                                                      |



---



\## 三、人脸姓名标签管理

python

SQLite数据库结构

faces = {

"face\_id": "789abc", # 人脸特征向量哈希值

"detected\_photos": \["/2025/09-13/IMG\_001.jpg"], # 检测到该人脸的图片路径

"user\_tag": "李明", # 用户设置的姓名标签

"last\_modified": "2025-09-13" # 最后编辑时间

}

---



\## 四、视频生成音乐方案

| \*\*要素\*\*         | \*\*实现方式\*\*                                                                |

|------------------|-----------------------------------------------------------------------------|

| 音乐来源         | FreePD.com API（免版税音乐）                                                |

| 主题匹配         | 家庭聚会→轻快钢琴曲 / 旅行风景→舒缓弦乐                                     |

| 音频处理         | 截取30秒高潮片段，生成后自动删除缓存                                        |



---



\## 五、硬件性能边界

mermaid

graph TD

A\[人脸识别] -->|峰值| B(CPU ≤65% / GPU ≤45%)

C\[视频生成] -->|峰值| D(CPU ≤80% / GPU ≤70%)

E\[音乐下载] -->|峰值| F(带宽 ≤5MB/s)



---



\## 六、交付物清单

| \*\*输出内容\*\*              | \*\*说明\*\*                                      |

|---------------------------|----------------------------------------------|

| `SortedPhotos/`           | 结构化照片库                                 |

| `Face\_Database.db`        | 人脸-姓名映射数据库                          |

| `Scene\_Tags.json`         | AI生成的场景标签库                           |

| `Events\_Videos/`          | 生成的短视频（1080P/MP4）                    |

| `PhotoWall\_Preview/`      | 网页版电子相册                               |



---



\## 七、隐私与安全

1\. 姓名标签采用AES-256加密存储

2\. 所有人脸数据仅限本地使用

3\. 网络请求强制HTTPS+证书验证



---



\## 八、性能验证指标

| \*\*任务\*\*               | \*\*目标值\*\*          | \*\*测试条件\*\*               |

|------------------------|---------------------|--------------------------|

| 照片迁移速度           | ≥3000张/分钟       | JPEG格式                 |

| 人脸识别速度           | ≈5张/秒            | 单线程处理               |

| 200张→1080P视频        | ≤10分钟            | 硬件编码启用              |

| 人脸数据库查询         | 1000次查询/秒      | 10,000条记录             |



